package gospeedmeter

import (
	"fmt"
	"math"
	"sort"
	"sync"
	"time"

	golog "github.com/real-rm/golog"
)

// Unit represents time units for speed calculation
type Unit string

const (
	UnitMS Unit = "ms"
	UnitS  Unit = "s"
	UnitM  Unit = "m"
	UnitH  Unit = "h"
	UnitD  Unit = "d"
)

var unitMap = map[Unit]int64{
	UnitMS: 1,
	UnitS:  1000,
	UnitM:  60000,
	UnitH:  3600000,
	UnitD:  3600000 * 24,
}

const AMOUNT_UNIT = "KMBT"

// SpeedMeter handles speed calculations for multiple meters
type SpeedMeter struct {
	values               map[string]float64
	mu                   sync.Mutex
	startTime            time.Time
	intervalCallback     func(*SpeedMeter)
	intervalTriggerCount int
	counter              int
	expirationDuration   time.Duration
}

// SpeedMeterOptions defines the configuration options for SpeedMeter
type SpeedMeterOptions struct {
	Values               map[string]float64
	IntervalCallback     func(*SpeedMeter)
	IntervalTriggerCount int
	ExpirationHours      int // Expiration time in hours, 0 means no expiration
}

// NewSpeedMeter creates a new SpeedMeter instance
func NewSpeedMeter(options SpeedMeterOptions) *SpeedMeter {
	values := make(map[string]float64)
	intervalCallback := func(sm *SpeedMeter) {
		golog.Info(sm.ToString(UnitM, nil))
	}
	intervalTriggerCount := 1000
	var expirationDuration time.Duration

	if options.Values != nil {
		values = options.Values
	}
	if options.IntervalCallback != nil {
		intervalCallback = options.IntervalCallback
	}
	if options.IntervalTriggerCount > 0 {
		intervalTriggerCount = options.IntervalTriggerCount
	}
	if options.ExpirationHours > 0 {
		expirationDuration = time.Duration(options.ExpirationHours) * time.Hour
	}

	return &SpeedMeter{
		values:               values,
		startTime:            time.Now(),
		intervalCallback:     intervalCallback,
		intervalTriggerCount: intervalTriggerCount,
		expirationDuration:   expirationDuration,
	}
}

// Reset resets the speed meter with optional initial values
func (sm *SpeedMeter) Reset(values ...map[string]float64) {
	if len(values) > 0 {
		sm.values = values[0]
	} else {
		sm.values = make(map[string]float64)
	}
	sm.startTime = time.Now()
}

// Check updates a single meter value and triggers callback if needed
func (sm *SpeedMeter) Check(name string, value float64) {
	sm.mu.Lock()

	// Check if expired before updating values
	if sm.expirationDuration > 0 && time.Since(sm.startTime) >= sm.expirationDuration {
		// Get current state for logging (without calling ToString to avoid deadlock)
		speed := sm.getSpeedUnsafe(UnitM)
		counters := make(map[string]float64)
		for k, v := range sm.values {
			counters[k] = v
		}

		// Log asynchronously to avoid blocking while holding the lock
		go func(duration time.Duration, counters map[string]float64, speed map[string]float64) {
			golog.Info(fmt.Sprintf("SpeedMeter expired after %v hours, counters: %v, speed: %v/m",
				duration.Hours(), counters, speed))
		}(sm.expirationDuration, counters, speed)

		// Reset the meter
		sm.values = make(map[string]float64)
		sm.startTime = time.Now()
		sm.counter = 0
	}

	sm.values[name] += value
	sm.counter++
	counter := sm.counter
	sm.mu.Unlock()

	if sm.intervalCallback != nil && counter%sm.intervalTriggerCount == 0 {
		sm.intervalCallback(sm)
	}
}

// getSpeedUnsafe returns the current speed for all meters without locking (internal use only)
func (sm *SpeedMeter) getSpeedUnsafe(unit Unit) map[string]float64 {
	denominator, ok := unitMap[unit]
	if !ok {
		denominator = unitMap[UnitS] // default to seconds
	}
	speed := make(map[string]float64)
	ms := time.Since(sm.startTime).Milliseconds()
	if ms == 0 {
		ms = 1 // prevent divide by 0
	}
	for k, v := range sm.values {
		speed[k] = float64(v) * float64(denominator) / float64(ms)
	}
	return speed
}

// GetSpeed returns the current speed for all meters in the specified unit
func (sm *SpeedMeter) GetSpeed(unit Unit) map[string]float64 {
	sm.mu.Lock()
	speed := sm.getSpeedUnsafe(unit)
	sm.mu.Unlock()
	return speed
}

// ToString returns a formatted string representation of the speed meter
// Results are sorted by name in ascending order
func (sm *SpeedMeter) ToString(unit Unit, toBeEstimated map[string]float64) string {
	speed := sm.GetSpeed(unit)
	counters := sm.GetCounters()

	// 收集所有 meter 名称并按名称升序排序
	var names []string
	for name := range speed {
		names = append(names, name)
	}
	sort.Strings(names)

	var result []string
	for _, name := range names {
		v := speed[name]
		vStr := numberToShow(v)
		toShow := fmt.Sprintf("%s(%v):%s/%s", name, counters[name], vStr, unit)
		if toBeEstimated != nil {
			if target, ok := toBeEstimated[name]; ok {
				estimate := sm.Estimate(name, target, unit)
				toShow += fmt.Sprintf(" est:%s %s", numberToShow(estimate), unit)
			}
		}
		result = append(result, toShow)
	}
	return fmt.Sprintf("%v", result)
}

// Estimate calculates estimated time to reach target value for a single meter
func (sm *SpeedMeter) Estimate(name string, targetValue float64, unit Unit) float64 {
	speeds := sm.GetSpeed(unit)
	remaining := math.Max(targetValue-sm.values[name], 0)
	if speeds[name] > 0 {
		return remaining / speeds[name]
	}
	return 0
}

// GetCounters returns current counter values
func (sm *SpeedMeter) GetCounters() map[string]float64 {
	sm.mu.Lock()
	result := make(map[string]float64)
	for k, v := range sm.values {
		result[k] = v
	}
	sm.mu.Unlock()
	return result
}

// numberToShow formats a number with K/M/B/T units
func numberToShow(v float64) string {
	amountUnit := ""
	amountUnitAt := 0
	for v > 1000 && amountUnitAt < len(AMOUNT_UNIT) {
		v /= 1000
		amountUnit = string(AMOUNT_UNIT[amountUnitAt])
		amountUnitAt++
	}

	// Format the number with 4 decimal places and limit to 5 characters
	strV := fmt.Sprintf("%.4f", v)
	if len(strV) > 5 {
		strV = strV[:5]
	}
	return strV + amountUnit
}
