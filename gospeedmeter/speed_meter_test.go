package gospeedmeter

import (
	"strings"
	"testing"
	"time"
)

func TestNewSpeedMeter(t *testing.T) {
	options := SpeedMeterOptions{
		Values: map[string]float64{"meter1": 10},
	}
	sm := NewSpeedMeter(options)
	if sm == nil {
		t.<PERSON>rror("Expected non-nil SpeedMeter")
	}
	if len(sm.GetCounters()) != 1 {
		t.<PERSON><PERSON>("Expected 1 counter")
	}
}

func TestSpeedMeterCheck(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Test initial check
	sm.Check("meter1", 13)
	sm.Check("meter2", 200)
	counters := sm.GetCounters()
	if counters["meter1"] != 13 || counters["meter2"] != 200 {
		t.Error("Check values not properly updated")
	}

	// Test second check
	sm.Check("meter1", 10)
	sm.Check("meter3", 10)
	counters = sm.GetCounters()
	if counters["meter1"] != 23 || counters["meter2"] != 200 || counters["meter3"] != 10 {
		t.Error("Check values not properly accumulated")
	}
}

func TestSpeedMeterGetSpeed(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Add some values
	sm.Check("meter1", 100)

	// Wait a bit to ensure time difference
	time.Sleep(100 * time.Millisecond)

	// Get speed in different units
	speed := sm.GetSpeed(UnitS)
	if speed["meter1"] < 900 || speed["meter1"] > 1100 {
		t.Errorf("Speed should be approximately 1000/s, got %f", speed["meter1"])
	}
}

func TestSpeedMeterReset(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Add initial values
	sm.Check("meter1", 100)

	// Reset with values
	sm.Reset(map[string]float64{"meter1": 50})
	counters := sm.GetCounters()
	if counters["meter1"] != 50 {
		t.Error("Reset not working properly")
	}

	// Reset without values
	sm.Reset()
	counters = sm.GetCounters()
	if len(counters) != 0 {
		t.Error("Reset should clear counters")
	}
}

func TestSpeedMeterEstimate(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Add some values
	sm.Check("meter1", 100)

	// Wait a bit
	time.Sleep(100 * time.Millisecond)

	// Test estimation
	estimate := sm.Estimate("meter1", 1000, UnitS)
	expectedEstimate := 0.9 // estimate should be 9.0
	if estimate < expectedEstimate*0.8 || estimate > expectedEstimate*1.2 {
		t.Errorf("Estimate should be approximately %f, got %f", expectedEstimate, estimate)
	}
}

func TestSpeedMeterToString(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Add some values
	sm.Check("meter1", 100)

	// Test string representation
	str := sm.ToString(UnitS, nil)
	if str == "" {
		t.Error("String representation should not be empty")
	}

	// Test string with estimates
	str = sm.ToString(UnitS, map[string]float64{"meter1": 1000})
	if str == "" {
		t.Error("String representation with estimates should not be empty")
	}
}

func TestSpeedMeterIntervalCallback(t *testing.T) {
	callbackCalled := false
	callback := func(sm *SpeedMeter) {
		callbackCalled = true
	}

	options := SpeedMeterOptions{
		IntervalTriggerCount: 2,
		IntervalCallback:     callback,
	}
	sm := NewSpeedMeter(options)

	// Add values to trigger callback
	sm.Check("meter1", 1)
	sm.Check("meter1", 1)

	if !callbackCalled {
		t.Error("Interval callback should have been called")
	}
}

func TestSpeedMeterConcurrency(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Test concurrent access
	done := make(chan bool)
	for i := 0; i < 10; i++ {
		go func() {
			sm.Check("meter1", 1)
			done <- true
		}()
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}

	counters := sm.GetCounters()
	if counters["meter1"] != 10 {
		t.Error("Concurrent access not handled properly")
	}
}

func TestSpeedMeterToStringSorting(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// 添加不同数量的计数器值来测试按名称排序
	sm.Check("meter_c", 100) // 名称中间
	sm.Check("meter_a", 500) // 名称最前
	sm.Check("meter_b", 50)  // 名称中间

	// 获取字符串输出
	result := sm.ToString(UnitS, nil)

	// 验证输出不为空
	if result == "" {
		t.Error("ToString result should not be empty")
	}

	// 验证输出包含所有 meter 名称
	// 由于排序是按名称升序，meter_a 应该在最前面，不管计数器值大小
	t.Logf("Name sorting output: %s", result)

	// 验证排序：meter_a 应该在 meter_b 之前，meter_b 应该在 meter_c 之前
	// 通过检查字符串中名称出现的位置来验证排序
	posA := strings.Index(result, "meter_a")
	posB := strings.Index(result, "meter_b")
	posC := strings.Index(result, "meter_c")

	if posA == -1 || posB == -1 || posC == -1 {
		t.Error("All meter names should be present in the output")
	}

	if posA > posB || posB > posC {
		t.Error("Meters should be sorted by name: meter_a < meter_b < meter_c")
	}
}

func TestSpeedMeterToStringSortingWithDifferentCounters(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// 添加不同计数器值的 meter 来测试纯名称排序（忽略计数器值）
	sm.Check("zebra", 100) // 名称最后，中等数量
	sm.Check("alpha", 50)  // 名称最前，最小数量
	sm.Check("beta", 200)  // 名称中间，最大数量
	sm.Check("gamma", 75)  // 名称中间，中等数量

	// 获取字符串输出
	result := sm.ToString(UnitS, nil)

	// 验证输出不为空
	if result == "" {
		t.Error("ToString result should not be empty")
	}

	t.Logf("Name-only sorting output: %s", result)

	// 验证排序逻辑：无论计数器值如何，都应该按名称升序排列
	// alpha < beta < gamma < zebra
	posAlpha := strings.Index(result, "alpha")
	posBeta := strings.Index(result, "beta")
	posGamma := strings.Index(result, "gamma")
	posZebra := strings.Index(result, "zebra")

	if posAlpha == -1 || posBeta == -1 || posGamma == -1 || posZebra == -1 {
		t.Error("All meter names should be present in the output")
	}

	if posAlpha > posBeta || posBeta > posGamma || posGamma > posZebra {
		t.Error("Meters should be sorted by name only: alpha < beta < gamma < zebra")
	}
}

// TestNewSpeedMeterDefaultCallback 测试默认回调函数的覆盖率
func TestNewSpeedMeterDefaultCallback(t *testing.T) {
	// 测试使用默认选项创建 SpeedMeter
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// 验证默认值设置正确
	if sm == nil {
		t.Error("Expected non-nil SpeedMeter")
	}

	// 测试默认的 intervalTriggerCount
	// 通过多次调用 Check 来触发默认回调
	for i := 0; i < 1000; i++ {
		sm.Check("test", 1)
	}

	// 验证计数器正确累加
	counters := sm.GetCounters()
	if counters["test"] != 1000 {
		t.Errorf("Expected counter to be 1000, got %f", counters["test"])
	}
}

// TestGetSpeedInvalidUnit 测试无效单位的处理
func TestGetSpeedInvalidUnit(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})
	sm.Check("meter1", 100)

	// 等待一段时间
	time.Sleep(10 * time.Millisecond)

	// 测试无效单位，应该使用默认的秒单位
	speed := sm.GetSpeed("invalid_unit")

	if len(speed) == 0 {
		t.Error("Speed map should not be empty")
	}

	if speed["meter1"] <= 0 {
		t.Error("Speed should be positive for invalid unit (defaults to seconds)")
	}
}

// TestEstimateEdgeCases 测试 Estimate 函数的边界情况
func TestEstimateEdgeCases(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// 测试速度为0的情况
	sm.Check("meter1", 0)
	time.Sleep(10 * time.Millisecond)

	estimate := sm.Estimate("meter1", 100, UnitS)
	if estimate != 0 {
		t.Errorf("Estimate should be 0 when speed is 0, got %f", estimate)
	}

	// 测试目标值小于当前值的情况
	sm.Check("meter2", 200)
	time.Sleep(10 * time.Millisecond)

	estimate = sm.Estimate("meter2", 100, UnitS) // 目标值小于当前值200
	if estimate != 0 {
		t.Errorf("Estimate should be 0 when target is less than current, got %f", estimate)
	}

	// 测试不存在的 meter
	estimate = sm.Estimate("nonexistent", 100, UnitS)
	if estimate != 0 {
		t.Errorf("Estimate should be 0 for nonexistent meter, got %f", estimate)
	}
}

// TestSpeedMeterExpiration 测试过期时间功能
func TestSpeedMeterExpiration(t *testing.T) {
	// 创建一个1小时过期的SpeedMeter，但我们会手动修改startTime来模拟过期
	sm := NewSpeedMeter(SpeedMeterOptions{
		ExpirationHours: 1,
	})

	// 添加一些数据
	sm.Check("meter1", 100)
	sm.Check("meter2", 200)

	// 验证数据已添加
	counters := sm.GetCounters()
	if counters["meter1"] != 100 || counters["meter2"] != 200 {
		t.Error("Initial values not set correctly")
	}

	// 手动设置startTime为2小时前，模拟过期
	sm.mu.Lock()
	sm.startTime = time.Now().Add(-2 * time.Hour)
	sm.mu.Unlock()

	// 再次调用Check，应该触发过期重置
	sm.Check("meter3", 50)

	// 验证计数器已被重置，只有新添加的meter3存在
	counters = sm.GetCounters()
	if len(counters) != 1 || counters["meter3"] != 50 {
		t.Errorf("Expected only meter3 with value 50 after expiration, got %v", counters)
	}

	// 验证meter1和meter2已被清除
	if _, exists := counters["meter1"]; exists {
		t.Error("meter1 should be cleared after expiration")
	}
	if _, exists := counters["meter2"]; exists {
		t.Error("meter2 should be cleared after expiration")
	}
}

// TestSpeedMeterNoExpiration 测试不设置过期时间的情况
func TestSpeedMeterNoExpiration(t *testing.T) {
	// 创建一个没有过期时间的SpeedMeter
	sm := NewSpeedMeter(SpeedMeterOptions{
		ExpirationHours: 0, // 0表示不过期
	})

	// 添加数据
	sm.Check("meter1", 100)

	// 手动设置startTime为很久以前
	sm.mu.Lock()
	sm.startTime = time.Now().Add(-24 * time.Hour)
	sm.mu.Unlock()

	// 再次调用Check，不应该触发重置
	sm.Check("meter1", 50)

	// 验证数据累加正常，没有被重置
	counters := sm.GetCounters()
	if counters["meter1"] != 150 {
		t.Errorf("Expected meter1 to be 150 (no expiration), got %f", counters["meter1"])
	}
}
