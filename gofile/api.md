<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gofile

```go
import "github.com/real-rm/gofile"
```

## Index

- [Constants](<#constants>)
- [func DownloadAndReadWithRetry\(url string, maxRetries ...int\) \(\[\]byte, error\)](<#DownloadAndReadWithRetry>)
- [func DownloadAndResizeImage\(url string, width, height int\) \(image.Image, error\)](<#DownloadAndResizeImage>)
- [func DownloadAndSaveFile\(opts \*DownloadAndSaveFileOptions\) \(\[\]string, error\)](<#DownloadAndSaveFile>)
- [func DownloadAndSaveImageInDirs\(url string, savePaths \[\]string, compressWebP bool, maxRetries ...int\) \(map\[string\]string, error\)](<#DownloadAndSaveImageInDirs>)
- [func DownloadAndSaveImageInDirsWithCompression\(url string, savePaths \[\]string, compressWebP bool, compressToSize bool, targetSizeKB int, maxRetries ...int\) \(map\[string\]string, error\)](<#DownloadAndSaveImageInDirsWithCompression>)
- [func DownloadAndSaveImageWithSmartCompression\(url string, savePath string, targetSizeKB int, compressWebP bool, maxRetries ...int\) \(string, error\)](<#DownloadAndSaveImageWithSmartCompression>)
- [func ProcessImageDataWithResize\(imageData \[\]byte, targetSizeKB ...int\) \(\[\]byte, error\)](<#ProcessImageDataWithResize>)
- [func ResizeImageFromData\(imageData \[\]byte, width, height int\) \(image.Image, error\)](<#ResizeImageFromData>)
- [func ResizeImageToMaxDimension\(img image.Image, isWebP bool\) \(image.Image, int, error\)](<#ResizeImageToMaxDimension>)
- [func SaveImage\(img image.Image, savePath string, compressWebP bool\) \(string, error\)](<#SaveImage>)
- [func SetTestHTTPClient\(client \*http.Client\)](<#SetTestHTTPClient>)
- [type DownloadAndSaveFileOptions](<#DownloadAndSaveFileOptions>)
- [type ImageProcessingOptions](<#ImageProcessingOptions>)


## Constants

<a name="DEFAULT_RETRIES"></a>

```go
const (
    DEFAULT_RETRIES = 3
    // Memory limits for image processing
    MAX_IMAGE_MEMORY_MB  = 100              // Restored to 100MB but with better memory management
    MAX_IMAGE_SIZE_BYTES = 10 * 1024 * 1024 // 10MB limit with waiting instead of skipping

    // Image compression constants
    DEFAULT_TARGET_SIZE_KB = 300  // Default target size in KB for image compression
    MAX_DIMENSION_PIXELS   = 1280 // Maximum dimension (width or height) for compressed images
    DEFAULT_JPEG_QUALITY   = 80   // Default JPEG quality for compression

    // HTTP connection configuration
    HTTP_EXPECT_CONTINUE_TIMEOUT_SECONDS = 10    // ExpectContinueTimeout
    HTTP_IDLE_CONN_TIMEOUT_SECONDS       = 30    // IdleConnTimeout - increased for connection pooling
    HTTP_MAX_CONNS_PER_HOST              = 10    // MaxConnsPerHost - increased for better concurrency
    HTTP_MAX_IDLE_CONNS                  = 50    // MaxIdleConns - global idle connections
    HTTP_MAX_IDLE_CONNS_PER_HOST         = 5     // MaxIdleConnsPerHost - idle connections per host
    HTTP_KEEP_ALIVE_SECONDS              = 30    // KeepAlive timeout for connection reuse
    HTTP_MAX_RESPONSE_HEADER_BYTES       = 16384 // MaxResponseHeaderBytes (16KB)

    // Retry wait time multipliers
    RETRY_TIMEOUT_MULTIPLIER   = 3  // Multiplier for timeout retry wait time
    RETRY_GENERAL_MULTIPLIER   = 2  // Multiplier for general retry wait time
    RETRY_BODY_READ_MULTIPLIER = 10 // Multiplier for body reading timeout retry

    // File permissions
    DIR_PERMISSION  = 0755 // Directory creation permissions
    FILE_PERMISSION = 0644 // File write permissions

    // Memory management wait times
    LARGE_IMAGE_WAIT_MILLISECONDS = 500 // Wait time after processing large images
)
```

<a name="DownloadAndReadWithRetry"></a>
## func [DownloadAndReadWithRetry](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L304>)

```go
func DownloadAndReadWithRetry(url string, maxRetries ...int) ([]byte, error)
```

DownloadAndReadWithRetry downloads and reads the entire response body with retry logic This includes both the HTTP request and reading the response body

<a name="DownloadAndResizeImage"></a>
## func [DownloadAndResizeImage](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L866>)

```go
func DownloadAndResizeImage(url string, width, height int) (image.Image, error)
```

DownloadAndResizeImage downloads an image from URL and returns the resized image width and height are the target dimensions, the image will maintain its aspect ratio and fit within these dimensions This function now uses the modular approach: Download \-\> Resize

<a name="DownloadAndSaveFile"></a>
## func [DownloadAndSaveFile](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L1206>)

```go
func DownloadAndSaveFile(opts *DownloadAndSaveFileOptions) ([]string, error)
```

DownloadAndSaveFile downloads a file and saves it to the specified format Returns the path to the saved file and any error that occurred

<a name="DownloadAndSaveImageInDirs"></a>
## func [DownloadAndSaveImageInDirs](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L702>)

```go
func DownloadAndSaveImageInDirs(url string, savePaths []string, compressWebP bool, maxRetries ...int) (map[string]string, error)
```

DownloadAndSaveImageInDirs downloads an image from URL and saves it to specified paths savePaths is a list of full paths including filename and extension If compressWebP is true, attempts to convert the image to WebP format with compression Falls back to JPEG if WebP encoding is not available When compressWebP=false, converts to JPEG only if target extension is .jpg/.jpeg, otherwise saves original data Returns a map \[savePath\]savedPath and any error that occurred

<a name="DownloadAndSaveImageInDirsWithCompression"></a>
## func [DownloadAndSaveImageInDirsWithCompression](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L718>)

```go
func DownloadAndSaveImageInDirsWithCompression(url string, savePaths []string, compressWebP bool, compressToSize bool, targetSizeKB int, maxRetries ...int) (map[string]string, error)
```

DownloadAndSaveImageInDirsWithCompression downloads an image from URL and saves it to specified paths with optional smart compression savePaths is a list of full paths including filename and extension If compressWebP is true, ALL files will be saved in WebP format \(or JPEG as fallback\), regardless of file extension If compressWebP is false, files will be saved in JPEG format or original format based on conversion needs If compressToSize is true, intelligently compresses the image to targetSizeKB Returns a map \[savePath\]savedPath and any error that occurred

OPTIMIZATION NOTES: \- Uses the modular approach: Download \-\> Process \-\> Save \- Image processing is done ONCE for all paths to avoid redundant processing \- Fast path optimization saves raw data directly when no processing is needed \- Consistent format handling: compressWebP applies to ALL paths uniformly

<a name="DownloadAndSaveImageWithSmartCompression"></a>
## func [DownloadAndSaveImageWithSmartCompression](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L1296>)

```go
func DownloadAndSaveImageWithSmartCompression(url string, savePath string, targetSizeKB int, compressWebP bool, maxRetries ...int) (string, error)
```

DownloadAndSaveImageWithSmartCompression downloads image and intelligently compresses to specified size This is a convenience function for quick implementation of image compression functionality

<a name="ProcessImageDataWithResize"></a>
## func [ProcessImageDataWithResize](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L1054>)

```go
func ProcessImageDataWithResize(imageData []byte, targetSizeKB ...int) ([]byte, error)
```

ProcessImageDataWithResize processes image data and resizes if needed Returns the processed image data \(resized if necessary\) targetSizeKB is optional \- if not provided or \<= 0, uses DEFAULT\_TARGET\_SIZE\_KB

<a name="ResizeImageFromData"></a>
## func [ResizeImageFromData](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L815>)

```go
func ResizeImageFromData(imageData []byte, width, height int) (image.Image, error)
```

ResizeImageFromData decodes image data and resizes it to fit within the specified dimensions width and height are the target dimensions, the image will maintain its aspect ratio and fit within these dimensions

<a name="ResizeImageToMaxDimension"></a>
## func [ResizeImageToMaxDimension](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L1077>)

```go
func ResizeImageToMaxDimension(img image.Image, isWebP bool) (image.Image, int, error)
```

ResizeImageToMaxDimension resizes image by setting the larger dimension to MAX\_DIMENSION\_PIXELS while maintaining aspect ratio. This function assumes the caller has already determined that resizing is needed.

<a name="SaveImage"></a>
## func [SaveImage](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L662>)

```go
func SaveImage(img image.Image, savePath string, compressWebP bool) (string, error)
```

SaveImage saves the provided image data to the specified path If compressWebP is true, attempts to convert the image to WebP format with compression Falls back to JPEG if WebP encoding is not available Returns the final saved file path and any error that occurred

<a name="SetTestHTTPClient"></a>
## func [SetTestHTTPClient](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L147>)

```go
func SetTestHTTPClient(client *http.Client)
```

SetTestHTTPClient sets a custom HTTP client for testing purposes Call with nil to reset to default behavior

<a name="DownloadAndSaveFileOptions"></a>
## type [DownloadAndSaveFileOptions](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L1193-L1202>)

DownloadAndSaveFileOptions contains all options for downloading and saving files

```go
type DownloadAndSaveFileOptions struct {
    URL             string   // URL to download from
    SavePaths       []string // Paths to save the file to
    CompressWebP    bool     // Whether to compress as WebP (only for images)
    IsPhoto         bool     // Whether the file is a photo
    MaxRetries      int      // Maximum number of retry attempts
    CompressToSize  bool     // Whether to enable smart compression to target size
    TargetSizeKB    int      // Target file size in KB (default: 300)
    AtomicOperation bool     // Whether to use atomic file operations to prevent partial files
}
```

<a name="ImageProcessingOptions"></a>
## type [ImageProcessingOptions](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L419-L425>)

ImageProcessingOptions contains all options for image processing

```go
type ImageProcessingOptions struct {
    CompressWebP    bool   // Whether to compress to WebP format
    CompressToSize  bool   // Whether to compress to target size
    TargetSizeKB    int    // Target size in KB
    URLExtension    string // URL extension for format detection (optional)
    NeedsConversion bool   // Whether format conversion is needed
}
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
