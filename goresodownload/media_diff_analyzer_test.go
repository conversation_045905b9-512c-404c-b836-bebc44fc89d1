package goresodownload

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/real-rm/gohelper"
	levelStore "github.com/real-rm/golevelstore"
	gomongo "github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// setupTest sets up the test environment
func setupTest(t *testing.T) (*gomongo.MongoCollection, func(*gomongo.MongoCollection)) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	gohelper.SetRmbaseFileCfg(configPath)
	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}

	// Create merged collections for each board
	for _, table := range BoardMergedTable {
		// Create collection in test database instead of rni
		coll := gomongo.Coll("rni", table)
		if coll == nil {
			t.Fatalf("Failed to create collection %s", table)
		}
	}

	// Create test collection
	coll := gomongo.Coll("rni", "test")
	if coll == nil {
		t.Fatal("Failed to create test collection")
	}

	// Return cleanup function
	cleanup := func(coll *gomongo.MongoCollection) {
		// Clean up test collection
		if coll != nil {
			if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
				t.Errorf("Failed to cleanup test collection: %v", err)
			}
		}
		// Clean up merged collections
		for _, table := range BoardMergedTable {
			if mergedColl := gomongo.Coll("rni", table); mergedColl != nil {
				if _, err := mergedColl.DeleteMany(context.Background(), bson.M{}); err != nil {
					t.Errorf("Failed to cleanup merged collection %s: %v", table, err)
				}
			}
		}
	}

	return coll, cleanup
}

// TestMain runs before all tests
func TestMain(m *testing.M) {
	os.Exit(m.Run())
}

func TestGetOldMediaFromLogs(t *testing.T) {
	// Setup test environment
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	// Test cases
	testCases := []struct {
		name          string
		board         string
		sid           string
		insertDoc     bson.M
		expectedPho   []int32
		expectedDoc   []string
		expectedError bool
		errorContains string
	}{
		{
			name:  "empty_media_array",
			board: "CAR",
			sid:   "test-sid",
			insertDoc: bson.M{
				"_id":   "test-sid",
				"phoLH": primitive.A{},
				"docLH": primitive.A{},
			},
			expectedPho:   []int32{},
			expectedDoc:   []string{},
			expectedError: false,
		},
		{
			name:  "valid_media_array",
			board: "CAR",
			sid:   "test-sid-2",
			insertDoc: bson.M{
				"_id":   "test-sid-2",
				"phoLH": primitive.A{int32(1), int32(2)},
				"docLH": primitive.A{"3.pdf", "4.pdf"},
			},
			expectedPho:   []int32{1, 2},
			expectedDoc:   []string{"3.pdf", "4.pdf"},
			expectedError: false,
		},
		{
			name:  "invalid_board",
			board: "invalid-board",
			sid:   "test-sid-3",
			insertDoc: bson.M{
				"_id":   "test-sid-3",
				"phoLH": primitive.A{},
				"docLH": primitive.A{},
			},
			expectedPho:   nil,
			expectedDoc:   nil,
			expectedError: true,
			errorContains: "failed to get merged collection for board: invalid-board",
		},
		{
			name:  "missing_fields",
			board: "CAR",
			sid:   "test-sid-4",
			insertDoc: bson.M{
				"_id": "test-sid-4",
			},
			expectedPho:   []int32{},
			expectedDoc:   []string{},
			expectedError: false,
		},
		{
			name:  "invalid_phoLH_type",
			board: "CAR",
			sid:   "test-sid-5",
			insertDoc: bson.M{
				"_id":   "test-sid-5",
				"phoLH": "invalid",
				"docLH": primitive.A{},
			},
			expectedPho:   []int32{},
			expectedDoc:   []string{},
			expectedError: false,
		},
		{
			name:  "invalid_docLH_type",
			board: "CAR",
			sid:   "test-sid-6",
			insertDoc: bson.M{
				"_id":   "test-sid-6",
				"phoLH": primitive.A{},
				"docLH": "invalid",
			},
			expectedPho:   []int32{},
			expectedDoc:   []string{},
			expectedError: false,
		},
	}

	// Run test cases
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Get logs collection for the board
			logsTable, exists := BoardMergedTable[tc.board]
			if !exists {
				// For invalid board test case, we don't need to insert data
				if tc.expectedError {
					analyzer := NewMediaDiffAnalyzer()
					phoLH, docLH, _, err := analyzer.getOldLHListFromMergedTable(tc.sid, tc.board)
					if err == nil {
						t.Error("Expected error but got nil")
					} else if !strings.Contains(err.Error(), tc.errorContains) {
						t.Errorf("Expected error to contain '%s', got '%s'", tc.errorContains, err.Error())
					}
					assert.Equal(t, tc.expectedPho, phoLH)
					assert.Equal(t, tc.expectedDoc, docLH)
					return
				}
				t.Fatalf("Invalid board type: %s", tc.board)
			}

			// Get logs collection
			logsColl := gomongo.Coll("rni", logsTable)
			if logsColl == nil {
				t.Fatalf("Failed to get logs collection: %s", logsTable)
			}

			// Clean up existing data
			_, err := logsColl.DeleteMany(context.Background(), bson.M{"_id": tc.sid})
			if err != nil {
				t.Fatalf("Failed to cleanup existing data: %v", err)
			}

			// Insert test document
			_, err = logsColl.InsertOne(context.Background(), tc.insertDoc)
			if err != nil {
				t.Fatalf("Failed to insert test document: %v", err)
			}

			// Create analyzer
			analyzer := NewMediaDiffAnalyzer()

			// Get old media
			phoLH, docLH, _, err := analyzer.getOldLHListFromMergedTable(tc.sid, tc.board)

			// Check error
			if tc.expectedError {
				if err == nil {
					t.Error("Expected error but got nil")
				} else if tc.errorContains != "" && !strings.Contains(err.Error(), tc.errorContains) {
					t.Errorf("Expected error to contain '%s', got '%s'", tc.errorContains, err.Error())
				}
				return
			}

			// Check media arrays
			assert.Equal(t, tc.expectedPho, phoLH)
			assert.Equal(t, tc.expectedDoc, docLH)
		})
	}
}

func TestBoardLogsTable(t *testing.T) {
	tests := []struct {
		name     string
		board    string
		expected string
		exists   bool
	}{
		{"car board", "CAR", "mls_car_master_records", true},
		{"crea board", "DDF", "reso_crea_merged", true},
		{"bcre board", "BRE", "bridge_bcre_merged", true},
		{"rae board", "EDM", "mls_rae_master_records", true},
		{"treb board", "TRB", "reso_treb_evow_merged", true},
		{"invalid board", "invalid", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			table, exists := BoardMergedTable[tt.board]
			assert.Equal(t, tt.exists, exists)
			if exists {
				assert.Equal(t, tt.expected, table)
			}
		})
	}
}

func TestGenerateFileName(t *testing.T) {
	tests := []struct {
		name          string
		sid           string
		media         bson.M
		expectedError bool
	}{
		{
			name: "Valid photo media",
			sid:  "123",
			media: bson.M{
				"MediaKey":      "key1",
				"MediaCategory": "Photo",
				"MediaURL":      "http://example.com/1.jpg",
			},
			expectedError: false,
		},
		{
			name: "Valid document media",
			sid:  "123",
			media: bson.M{
				"MediaKey":      "key2",
				"MediaCategory": "Document",
				"MediaURL":      "http://example.com/2.pdf",
			},
			expectedError: false,
		},
		{
			name:          "Invalid media - nil",
			sid:           "123",
			media:         nil,
			expectedError: true,
		},
		{
			name: "Invalid media - missing key",
			sid:  "123",
			media: bson.M{
				"MediaCategory": "Photo",
				"MediaURL":      "http://example.com/1.jpg",
			},
			expectedError: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			analyzer := NewMediaDiffAnalyzer()
			fileName, err := analyzer.generateFileName(tc.sid, tc.media, "")

			if tc.expectedError {
				assert.Error(t, err)
				assert.Empty(t, fileName)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, fileName)
				assert.Contains(t, fileName, tc.sid)
			}
		})
	}
}

func TestExtractMediaInfo(t *testing.T) {
	tests := []struct {
		name          string
		media         bson.M
		isTreb        bool
		expectedError bool
	}{
		{
			name: "Valid regular board media",
			media: bson.M{
				"MediaKey":      "key1",
				"MediaCategory": "Photo",
				"MediaURL":      "http://example.com/1.jpg",
				"MimeType":      "image/jpeg",
			},
			isTreb:        false,
			expectedError: false,
		},
		{
			name: "Valid TREB media",
			media: bson.M{
				"key": "key1",
				"cat": "Photo",
				"url": "http://example.com/1.jpg",
				"tp":  "image/jpeg",
			},
			isTreb:        true,
			expectedError: false,
		},
		{
			name:          "Invalid media - nil",
			media:         nil,
			isTreb:        false,
			expectedError: true,
		},
		{
			name: "Invalid media - missing key",
			media: bson.M{
				"MediaCategory": "Photo",
				"MediaURL":      "http://example.com/1.jpg",
			},
			isTreb:        false,
			expectedError: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			analyzer := NewMediaDiffAnalyzer()
			info, err := analyzer.extractMediaInfo(tc.media, tc.isTreb)

			if tc.expectedError {
				assert.Error(t, err)
				assert.Nil(t, info)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, info)
				assert.NotEmpty(t, info.MediaKey)
				assert.NotEmpty(t, info.URL)
				assert.NotEmpty(t, info.Type)
				assert.NotEmpty(t, info.Category)
			}
		})
	}
}

func TestFilterMedias(t *testing.T) {
	tests := []struct {
		name           string
		medias         []bson.M
		expectedPhotos int
		expectedDocs   int
	}{
		{
			name: "Mixed media types",
			medias: []bson.M{
				{
					"id":       1,
					"key":      "key1",
					"cat":      "Photo",
					"url":      "http://example.com/1.jpg",
					"tp":       "image/jpeg",
					"status":   "Active",
					"sizeDesc": "Largest",
				},
				{
					"key":    "key2",
					"cat":    "Document",
					"url":    "http://example.com/2.pdf",
					"tp":     "application/pdf",
					"status": "Active",
				},
			},
			expectedPhotos: 1,
			expectedDocs:   1,
		},
		{
			name:           "Empty media list",
			medias:         []bson.M{},
			expectedPhotos: 0,
			expectedDocs:   0,
		},
		{
			name: "Invalid media",
			medias: []bson.M{
				{
					"key":    "key1",
					"status": "Inactive",
				},
			},
			expectedPhotos: 0,
			expectedDocs:   0,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			photos, docs := FilterMedias(tc.medias)
			assert.Equal(t, tc.expectedPhotos, len(photos))
			assert.Equal(t, tc.expectedDocs, len(docs))
		})
	}
}

func TestGenerateHashList(t *testing.T) {
	tests := []struct {
		name     string
		medias   []bson.M
		expected struct {
			photoHashes []int32
			docHashes   []string
		}
	}{
		{
			name: "Mixed media types",
			medias: []bson.M{
				{
					"MediaKey":      "key1",
					"MediaCategory": "Photo",
					"MediaURL":      "http://example.com/1.jpg",
				},
				{
					"MediaKey":      "key2",
					"MediaCategory": "Document",
					"MediaURL":      "http://example.com/2.pdf",
				},
			},
			expected: struct {
				photoHashes []int32
				docHashes   []string
			}{
				photoHashes: []int32{levelStore.MurmurToInt32("key1")},
				docHashes:   []string{fmt.Sprintf("%d.pdf", levelStore.MurmurToInt32("key2"))},
			},
		},
		{
			name:   "Empty media list",
			medias: []bson.M{},
			expected: struct {
				photoHashes []int32
				docHashes   []string
			}{
				photoHashes: []int32{},
				docHashes:   []string{},
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			analyzer := NewMediaDiffAnalyzer()
			photoHashes, docHashes := analyzer.generateHashList(tc.medias)
			assert.Equal(t, tc.expected.photoHashes, photoHashes)
			assert.Equal(t, tc.expected.docHashes, docHashes)
		})
	}
}

func TestGenerateDeleteTasks(t *testing.T) {
	// Setup test environment
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	tests := []struct {
		name          string
		deletedMedias []bson.M
		oldMedias     []bson.M
		sid           string
		filePath      string
		wantLen       int
	}{
		{
			name:          "single deleted media",
			deletedMedias: []bson.M{}, // Empty array since we're testing deletion
			oldMedias: []bson.M{
				{
					"key":      "abc",
					"cat":      "Photo",
					"url":      "http://example.com/1.jpg",
					"tp":       "image/jpeg",
					"id":       "1",
					"status":   "Active",
					"sizeDesc": "Largest",
				},
				{
					"key":      "def",
					"cat":      "Photo",
					"url":      "http://example.com/2.jpg",
					"tp":       "image/jpeg",
					"id":       "2",
					"status":   "Active",
					"sizeDesc": "Medium",
				},
			},
			sid:      "123",
			filePath: "/path/to/files",
			wantLen:  2, // Both media should be deleted since they're not in deletedMedias
		},
		{
			name:          "multiple deleted media",
			deletedMedias: []bson.M{}, // Empty array since we're testing deletion
			oldMedias: []bson.M{
				{
					"key":    "abc",
					"cat":    "Photo",
					"url":    "http://example.com/1.jpg",
					"tp":     "image/jpeg",
					"id":     "1",
					"status": "Active",
				},
				{
					"key":    "def",
					"cat":    "Photo",
					"url":    "http://example.com/2.jpg",
					"tp":     "image/jpeg",
					"id":     "2",
					"status": "Active",
				},
				{
					"key":    "ghi",
					"cat":    "Photo",
					"url":    "http://example.com/3.jpg",
					"tp":     "image/jpeg",
					"id":     "3",
					"status": "Active",
				},
				{
					"key":    "jkl",
					"cat":    "Photo",
					"url":    "http://example.com/4.jpg",
					"tp":     "image/jpeg",
					"id":     "4",
					"status": "Active",
				},
			},
			sid:      "456",
			filePath: "/path/to/files",
			wantLen:  4, // All media should be deleted since they're not in deletedMedias
		},
		{
			name:          "no deleted media",
			deletedMedias: []bson.M{},
			oldMedias:     []bson.M{},
			sid:           "789",
			filePath:      "/path/to/files",
			wantLen:       0,
		},
	}

	analyzer := NewMediaDiffAnalyzer()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Insert old media data
			if len(tt.oldMedias) > 0 {
				mergedColl := gomongo.Coll("rni", BoardMergedTable["TRB"])
				if mergedColl == nil {
					t.Fatalf("Failed to get merged collection for board: TRB")
				}

				// Clean up existing data
				_, err := mergedColl.DeleteMany(context.Background(), bson.M{"_id": tt.sid})
				if err != nil {
					t.Fatalf("Failed to cleanup existing data: %v", err)
				}

				// Generate hash lists for old media
				phoLH, docLH := analyzer.generateHashList(tt.oldMedias)
				assert.Equal(t, len(phoLH)+len(docLH), len(tt.oldMedias))

				// Insert document with old media hashes
				oldDoc := bson.M{
					"_id":   tt.sid,
					"phoLH": phoLH,
					"docLH": docLH,
				}
				_, err = mergedColl.InsertOne(context.Background(), oldDoc)
				if err != nil {
					t.Fatalf("Failed to insert old media data: %v", err)
				}

			}

			// Since generateTrebDeleteTasks is now a private method, we'll test it through
			// the public Analyze method
			phoLH, docLH := analyzer.generateHashList(tt.oldMedias)
			newProp := bson.M{
				"ListingKey": tt.sid, // Use ListingKey for TRB board
				"_id":        tt.sid,
				"media":      tt.deletedMedias, // Empty array since we're testing deletion
				"phoLH":      phoLH,            // Use phoLH from old media
				"docLH":      docLH,            // Use docLH from old media
				"ts":         primitive.NewDateTimeFromTime(time.Now()),
			}
			result, err := analyzer.Analyze(newProp, "TRB", "ca6")
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			assert.Equal(t, tt.wantLen, len(result.DeleteTasks))
		})
	}
}

func TestMediaDiffAnalyzerRegularBoard(t *testing.T) {
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	// Create test data
	now := time.Now()
	newProp := bson.M{
		"_id":       "test123ID",
		"ListingId": "test123",
		"Media": []interface{}{
			bson.M{
				"MediaKey":      "key1",
				"MediaURL":      "http://example.com/1.jpg",
				"MimeType":      "image/jpeg",
				"MediaCategory": "Photo",
				"Order":         1,
				"hash":          int32(levelStore.MurmurToInt32("key1")),
			},
			bson.M{
				"MediaKey":      "key2",
				"MediaURL":      "http://example.com/2.pdf",
				"MimeType":      "application/pdf",
				"MediaCategory": "Document",
				"Order":         2,
				"hash":          int32(levelStore.MurmurToInt32("key2")),
			},
		},
		"ts": now,
	}

	// Create analyzer and analyze
	analyzer := NewMediaDiffAnalyzer()
	result, err := analyzer.Analyze(newProp, "CAR", "ca6")
	if err != nil {
		t.Fatalf("Failed to analyze: %v", err)
	}

	// Verify results
	assert.Equal(t, 2, len(result.DownloadTasks), "Expected 2 download tasks")
	assert.Equal(t, 0, len(result.DeleteTasks), "Expected 0 delete tasks")
	assert.Equal(t, 1, len(result.PhoLH), "Expected 1 photo hash")
	assert.Equal(t, 1, len(result.DocLH), "Expected 1 document hash")

	// Verify download tasks
	foundPhoto := false
	foundDoc := false
	for _, task := range result.DownloadTasks {
		assert.Equal(t, "test123", task.Sid, "Expected sid test123")
		switch task.MediaKey {
		case "key1":
			assert.Equal(t, "http://example.com/1.jpg", task.URL, "Expected URL http://example.com/1.jpg")
			assert.Equal(t, "image/jpeg", task.Type, "Expected type image/jpeg")
			assert.True(t, task.IsPhoto, "Expected IsPhoto to be true")
			foundPhoto = true
		case "key2":
			assert.Equal(t, "http://example.com/2.pdf", task.URL, "Expected URL http://example.com/2.pdf")
			assert.Equal(t, "application/pdf", task.Type, "Expected type application/pdf")
			assert.False(t, task.IsPhoto, "Expected IsPhoto to be false")
			foundDoc = true
		default:
			t.Errorf("Unexpected mediaKey: %s", task.MediaKey)
		}
	}
	assert.True(t, foundPhoto, "Expected to find photo download task")
	assert.True(t, foundDoc, "Expected to find document download task")
}

func TestMediaDiffAnalyzerTrebBoard(t *testing.T) {
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	// Create test data
	now := time.Now()
	newProp := bson.M{
		"_id":        "test123ID",
		"ListingKey": "test123", // Use ListingKey for TRB board
		"media": []interface{}{ // Use media field for TRB board
			bson.M{
				"key":    "key1",
				"url":    "http://example.com/1.jpg",
				"tp":     "image/jpeg",
				"cat":    "Photo",
				"id":     1,
				"status": "Active",
			},
			bson.M{
				"key":    "key2",
				"url":    "http://example.com/2.pdf",
				"tp":     "application/pdf",
				"cat":    "Document",
				"id":     "2",
				"status": "Active",
			},
		},
		"ts": now,
	}

	// Create test directory
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	testDir := filepath.Join(currentDir, "test_data")
	if err := os.MkdirAll(testDir, 0755); err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}
	defer func() {
		if err := os.RemoveAll(testDir); err != nil {
			t.Fatalf("Failed to remove test directory: %v", err)
		}
	}()

	// Create analyzer and analyze
	analyzer := NewMediaDiffAnalyzer()
	result, err := analyzer.Analyze(newProp, "TRB", "ca6")
	if err != nil {
		t.Fatalf("Failed to analyze: %v", err)
	}

	// Verify results
	if len(result.DownloadTasks) != 2 {
		t.Errorf("Expected 2 download tasks, got %d", len(result.DownloadTasks))
	}
	if len(result.DeleteTasks) != 0 {
		t.Errorf("Expected 0 delete tasks, got %d", len(result.DeleteTasks))
	}
	if len(result.PhoLH) != 1 {
		t.Errorf("Expected 1 photo hash, got %d", len(result.PhoLH))
	}
	if len(result.DocLH) != 1 {
		t.Errorf("Expected 1 document hash, got %d", len(result.DocLH))
	}

	// Verify download tasks
	for _, task := range result.DownloadTasks {
		if task.Sid != "test123" {
			t.Errorf("Expected sid test123, got %s", task.Sid)
		}
		switch task.MediaKey {
		case "key1":
			if task.URL != "http://example.com/1.jpg" {
				t.Errorf("Expected URL http://example.com/1.jpg, got %s", task.URL)
			}
			if task.Type != "image/jpeg" {
				t.Errorf("Expected type image/jpeg, got %s", task.Type)
			}
			if !task.IsPhoto {
				t.Error("Expected IsPhoto to be true")
			}
		case "key2":
			if task.URL != "http://example.com/2.pdf" {
				t.Errorf("Expected URL http://example.com/2.pdf, got %s", task.URL)
			}
			if task.Type != "application/pdf" {
				t.Errorf("Expected type application/pdf, got %s", task.Type)
			}
			if task.IsPhoto {
				t.Error("Expected IsPhoto to be false")
			}
		default:
			t.Errorf("Unexpected mediaKey: %s", task.MediaKey)
		}
	}
}

func TestMediaDiffAnalyzerInvalidBoard(t *testing.T) {
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	// Create test data
	newProp := bson.M{
		"ListingId": "test123",
		"Media":     []interface{}{},
		"ts":        time.Now(),
	}

	// Create analyzer and analyze
	analyzer := NewMediaDiffAnalyzer()
	_, err := analyzer.Analyze(newProp, "INVALID", "ca6")
	if err == nil {
		t.Error("Expected error for invalid board")
	}
}

func TestMediaDiffAnalyzerInvalidChangeDoc(t *testing.T) {
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	// Create test data with invalid format
	newProp := bson.M{
		"Media": "invalid",
		"ts":    time.Now(),
	}

	// Create analyzer and analyze
	analyzer := NewMediaDiffAnalyzer()
	_, err := analyzer.Analyze(newProp, "CAR", "ca6")
	if err == nil {
		t.Error("Expected error for invalid change document")
	}
}

func TestGetOldLHListFromMergedTable(t *testing.T) {
	tests := []struct {
		name          string
		id            string
		board         string
		mergedData    bson.M
		expectedError bool
		errorContains string
		expectedPho   []int32
		expectedDoc   []string
	}{
		{
			name:  "Valid merged document",
			id:    "123",
			board: "CAR",
			mergedData: bson.M{
				"_id":   "123",
				"phoLH": primitive.A{int32(1), int32(2), int32(3)},
				"docLH": primitive.A{"1.pdf", "2.pdf"},
			},
			expectedError: false,
			expectedPho:   []int32{1, 2, 3},
			expectedDoc:   []string{"1.pdf", "2.pdf"},
		},
		{
			name:          "Document not found",
			id:            "nonexistent",
			board:         "CAR",
			mergedData:    bson.M{},
			expectedError: false,
			expectedPho:   []int32{},
			expectedDoc:   []string{},
		},
		{
			name:          "Invalid board",
			id:            "123",
			board:         "INVALID",
			mergedData:    bson.M{},
			expectedError: true,
			errorContains: "failed to get merged collection",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Setup test collection
			coll, cleanup := setupTest(t)
			defer cleanup(coll)

			// Insert test data if needed
			if len(tc.mergedData) > 0 {
				mergedColl := gomongo.Coll("rni", BoardMergedTable[tc.board])
				if mergedColl == nil {
					t.Fatalf("Failed to get merged collection for board: %s", tc.board)
				}
				_, err := mergedColl.InsertOne(context.Background(), tc.mergedData)
				if err != nil {
					t.Fatalf("Failed to insert test data: %v", err)
				}
			}

			// Test getOldLHListFromMergedTable
			analyzer := NewMediaDiffAnalyzer()
			phoLH, docLH, _, err := analyzer.getOldLHListFromMergedTable(tc.id, tc.board)

			if tc.expectedError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errorContains)
				assert.Nil(t, phoLH)
				assert.Nil(t, docLH)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expectedPho, phoLH)
				assert.Equal(t, tc.expectedDoc, docLH)
			}
		})
	}
}

func TestGetNewMediaInfoErrorCases(t *testing.T) {
	tests := []struct {
		name          string
		changeDoc     bson.M
		board         string
		expectedError string
	}{
		{
			name: "Missing fullDocument",
			changeDoc: bson.M{
				"other": "field",
			},
			board:         "CAR",
			expectedError: "invalid listingId or _id field format in newProp",
		},
		{
			name: "Missing ListingId",
			changeDoc: bson.M{
				"_id": "test123",
			},
			board:         "CAR",
			expectedError: "invalid listingId or _id field format in newProp",
		},
		{
			name: "Missing _id",
			changeDoc: bson.M{
				"ListingId": "test123",
			},
			board:         "CAR",
			expectedError: "invalid listingId or _id field format in newProp",
		},
		{
			name: "Invalid Media field type",
			changeDoc: bson.M{
				"ListingId": "test123",
				"_id":       "test123",
				"Media":     "invalid",
			},
			board:         "CAR",
			expectedError: "invalid media field format in newProp",
		},
		{
			name: "Invalid ts field type",
			changeDoc: bson.M{
				"ListingId": "test123",
				"_id":       "test123",
				"Media":     []bson.M{},
				"ts":        "invalid",
			},
			board:         "CAR",
			expectedError: "no valid timestamp found in record for board CAR",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			analyzer := NewMediaDiffAnalyzer()
			_, err := analyzer.getNewMediaInfo(tc.changeDoc, tc.board)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), tc.expectedError)
		})
	}
}

func TestExtractMediaInfoEdgeCases(t *testing.T) {
	tests := []struct {
		name          string
		media         bson.M
		isTreb        bool
		expectedError string
	}{
		{
			name:          "Nil media",
			media:         nil,
			isTreb:        false,
			expectedError: "media info is nil",
		},
		{
			name: "Missing media key",
			media: bson.M{
				"MediaURL": "http://example.com/1.jpg",
				"MimeType": "image/jpeg",
			},
			isTreb:        false,
			expectedError: "missing media key",
		},
		{
			name: "Missing URL",
			media: bson.M{
				"MediaKey": "key1",
			},
			isTreb:        false,
			expectedError: "missing URL",
		},
		{
			name: "Missing media type",
			media: bson.M{
				"MediaKey":      "key1",
				"MediaURL":      "http://example.com/1.jpg",
				"MediaCategory": "Photo",
			},
			isTreb:        false,
			expectedError: "",
		},
		{
			name: "Missing media category",
			media: bson.M{
				"MediaKey": "key1",
				"MediaURL": "http://example.com/1.jpg",
				"MimeType": "image/jpeg",
			},
			isTreb:        false,
			expectedError: "",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			analyzer := NewMediaDiffAnalyzer()
			info, err := analyzer.extractMediaInfo(tc.media, tc.isTreb)

			if tc.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedError)
				assert.Nil(t, info)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, info)
				if info != nil {
					assert.Equal(t, "image/jpeg", info.Type)
				}
			}
		})
	}
}

func TestFilterMediasEdgeCases(t *testing.T) {
	tests := []struct {
		name           string
		medias         []bson.M
		expectedPhotos int
		expectedDocs   int
	}{
		{
			name:           "Empty media list",
			medias:         []bson.M{},
			expectedPhotos: 0,
			expectedDocs:   0,
		},
		{
			name: "Invalid media - missing URL",
			medias: []bson.M{
				{
					"key":    "key1",
					"status": "Active",
				},
			},
			expectedPhotos: 0,
			expectedDocs:   0,
		},
		{
			name: "Invalid media - inactive status",
			medias: []bson.M{
				{
					"key":    "key1",
					"url":    "http://example.com/1.jpg",
					"status": "Inactive",
				},
			},
			expectedPhotos: 0,
			expectedDocs:   0,
		},
		{
			name: "Multiple photos with different sizes",
			medias: []bson.M{
				{
					"id":       1,
					"key":      "key1",
					"url":      "http://example.com/1.jpg",
					"tp":       "image/jpeg",
					"cat":      "Photo",
					"status":   "Active",
					"sizeDesc": "Thumbnail",
				},
				{
					"id":       1,
					"key":      "key1",
					"url":      "http://example.com/1_large.jpg",
					"tp":       "image/jpeg",
					"cat":      "Photo",
					"status":   "Active",
					"sizeDesc": "Large",
				},
				{
					"id":       1,
					"key":      "key1",
					"url":      "http://example.com/1_largest.jpg",
					"tp":       "image/jpeg",
					"cat":      "Photo",
					"status":   "Active",
					"sizeDesc": "Largest",
				},
			},
			expectedPhotos: 1,
			expectedDocs:   0,
		},
		{
			name: "Mixed media types with invalid entries",
			medias: []bson.M{
				{
					"id":       1,
					"key":      "key1",
					"url":      "http://example.com/1.jpg",
					"tp":       "image/jpeg",
					"cat":      "Photo",
					"status":   "Active",
					"sizeDesc": "Largest",
				},
				{
					"key":    "key2",
					"url":    "http://example.com/2.pdf",
					"tp":     "application/pdf",
					"cat":    "Document",
					"status": "Active",
				},
				{
					"key":    "key3",
					"status": "Active",
				},
				{
					"key":    "key4",
					"url":    "http://example.com/4.jpg",
					"status": "Inactive",
				},
			},
			expectedPhotos: 1,
			expectedDocs:   1,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			photos, docs := FilterMedias(tc.medias)
			assert.Equal(t, tc.expectedPhotos, len(photos))
			assert.Equal(t, tc.expectedDocs, len(docs))
		})
	}
}

func TestGenerateFileNameErrorCases(t *testing.T) {
	tests := []struct {
		name          string
		sid           string
		media         bson.M
		expectedError string
	}{
		{
			name:          "Nil media",
			sid:           "test123",
			media:         nil,
			expectedError: "media info is nil",
		},
		{
			name: "Missing media key",
			sid:  "test123",
			media: bson.M{
				"MediaURL": "http://example.com/1.jpg",
			},
			expectedError: "missing media key",
		},
		{
			name: "Missing URL",
			sid:  "test123",
			media: bson.M{
				"MediaKey": "key1",
			},
			expectedError: "missing URL",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			analyzer := NewMediaDiffAnalyzer()
			fileName, err := analyzer.generateFileName(tc.sid, tc.media, "")
			assert.Error(t, err)
			assert.Contains(t, err.Error(), tc.expectedError)
			assert.Empty(t, fileName)
		})
	}
}

func TestGenerateHashListEdgeCases(t *testing.T) {
	tests := []struct {
		name           string
		medias         []bson.M
		expectedPhotos int
		expectedDocs   int
	}{
		{
			name:           "Empty media list",
			medias:         []bson.M{},
			expectedPhotos: 0,
			expectedDocs:   0,
		},
		{
			name: "Mixed media types",
			medias: []bson.M{
				{
					"MediaKey":      "key1",
					"MediaCategory": "Photo",
					"MediaURL":      "http://example.com/1.jpg",
				},
				{
					"MediaKey":      "key2",
					"MediaCategory": "Document",
					"MediaURL":      "http://example.com/2.pdf",
				},
				{
					"key": "key3",
					"cat": "Photo",
					"url": "http://example.com/3.jpg",
				},
				{
					"key": "key4",
					"cat": "Document",
					"url": "http://example.com/4.pdf",
				},
			},
			expectedPhotos: 2,
			expectedDocs:   2,
		},
		{
			name: "Invalid media entries (no MediaKey)",
			medias: []bson.M{
				{"MediaCategory": "Photo"},
				{"MediaCategory": "Document"},
			},
			expectedPhotos: 0,
			expectedDocs:   0,
		},
		{
			name: "Only MediaKey present",
			medias: []bson.M{
				{"MediaCategory": "Photo", "MediaKey": "key1"},
				{"MediaCategory": "Document", "MediaKey": "key2"},
			},
			expectedPhotos: 1,
			expectedDocs:   1,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			analyzer := NewMediaDiffAnalyzer()
			photoHashes, docHashes := analyzer.generateHashList(tc.medias)
			assert.Equal(t, tc.expectedPhotos, len(photoHashes))
			assert.Equal(t, tc.expectedDocs, len(docHashes))
		})
	}
}

func TestPrepareMediaTasks_InvalidDocLHFormat(t *testing.T) {
	analyzer := NewMediaDiffAnalyzer()
	info := &MediaTaskInfo{
		Sid:          "sid",
		NewMedia:     []bson.M{},
		OldPhoLHList: []int32{},
		OldDocLHList: []string{"notanumber.pdf", "123"}, // invalid format
		Timestamp:    time.Now(),
		Board:        "CAR",
	}
	// gofile.GetFullFilePath 需要能正常返回
	_, _, err := analyzer.prepareMediaTasks(info)
	assert.Error(t, err) // 因为 GetFullFilePath 依赖外部实现，可能会报错
}

func TestGenerateDeleteTasks_Int32ToBase62Error(t *testing.T) {
	// Create a variable to hold the original function
	int32ToBase62 := levelStore.Int32ToBase62
	// Restore original function after test
	defer func() {
		levelStore.Int32ToBase62 = int32ToBase62
	}()

	// Mock the function to always return error
	mockCalled := false
	levelStore.Int32ToBase62 = func(n int32) (string, error) {
		mockCalled = true
		return "", fmt.Errorf("mock error")
	}

	analyzer := NewMediaDiffAnalyzer()
	deleteTasks := analyzer.generateDeleteTasks([]struct {
		hash    int32
		isPhoto bool
		ext     string
	}{{hash: -1, isPhoto: true, ext: ".jpg"}}, "sid", "/tmp")

	assert.True(t, mockCalled, "Mock function was not called")
	assert.Len(t, deleteTasks, 0)
}

func TestGenerateFileName_NoExt(t *testing.T) {
	analyzer := NewMediaDiffAnalyzer()
	media := bson.M{"MediaKey": "key1", "MediaURL": "http://example.com/1"} // no ext
	fileName, err := analyzer.generateFileName("sid", media, "")
	assert.NoError(t, err)
	assert.True(t, strings.HasSuffix(fileName, ".jpg")) // Default is now .jpg for unknown types
}

func TestGenerateFileName_Int32ToBase62Error(t *testing.T) {
	analyzer := NewMediaDiffAnalyzer()
	// 用特殊 key 让 gofile.MurmurToInt32 返回 -1，假设 -1 会让 Int32ToBase62 报错
	media := bson.M{"MediaKey": string([]byte{0xff, 0xff, 0xff, 0xff}), "MediaURL": "http://example.com/1.jpg"}
	_, err := analyzer.generateFileName("sid", media, "")
	// 只要 Int32ToBase62 报错就能覆盖
	if err != nil {
		assert.Error(t, err)
	}
}

func TestExtractMediaInfo_Defaults(t *testing.T) {
	analyzer := NewMediaDiffAnalyzer()
	media := bson.M{"MediaKey": "key1", "MediaURL": "http://example.com/1.jpg"} // no MimeType, no MediaCategory
	info, err := analyzer.extractMediaInfo(media, false)
	assert.NoError(t, err)
	assert.Equal(t, "image/jpeg", info.Type)
}

func TestGetOldLHListFromMergedTable_MongoError(t *testing.T) {
	analyzer := NewMediaDiffAnalyzer()
	// 用非法 board 触发 collName==""
	_, _, _, err := analyzer.getOldLHListFromMergedTable("id", "NOTEXIST")
	assert.Error(t, err)
}

func TestMediaDiffAnalyzer_EdgeCases(t *testing.T) {
	// Setup test environment
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	analyzer := NewMediaDiffAnalyzer()
	testID := primitive.NewObjectID().Hex()

	testCases := []struct {
		name          string
		changeDoc     bson.M
		boardType     string
		expectedError string
		expectNil     bool
	}{
		{
			name: "empty_change_doc",
			changeDoc: bson.M{
				"_id": testID,
			},
			boardType:     "CAR",
			expectedError: "invalid listingId or _id field format in newProp",
			expectNil:     false,
		},
		{
			name: "missing_fullDocument",
			changeDoc: bson.M{
				"_id": testID,
			},
			boardType:     "CAR",
			expectedError: "invalid listingId or _id field format in newProp",
			expectNil:     false,
		},
		{
			name: "invalid_media_format",
			changeDoc: bson.M{
				"_id":       testID,
				"ListingId": "123",
				"Media":     "invalid",
				"ts":        primitive.NewDateTimeFromTime(time.Now()),
			},
			boardType:     "CAR",
			expectedError: "invalid media field format in newProp",
			expectNil:     false,
		},
		{
			name: "invalid_media_array",
			changeDoc: bson.M{
				"_id":       testID,
				"ListingId": "123",
				"Media":     []string{"invalid"},
				"ts":        primitive.NewDateTimeFromTime(time.Now()),
			},
			boardType:     "CAR",
			expectedError: "invalid media field format in newProp",
			expectNil:     false,
		},
		{
			name: "invalid_media_url",
			changeDoc: bson.M{
				"_id":       testID,
				"ListingId": "123",
				"Media": []bson.M{
					{
						"key": "test",
						"url": "invalid-url",
					},
				},
				"ts": primitive.NewDateTimeFromTime(time.Now()),
			},
			boardType:     "CAR",
			expectedError: "",
			expectNil:     false,
		},
		{
			name: "missing_media_key",
			changeDoc: bson.M{
				"_id":       testID,
				"ListingId": "123",
				"Media": []bson.M{
					{
						"url": "http://example.com/test.jpg",
					},
				},
				"ts": primitive.NewDateTimeFromTime(time.Now()),
			},
			boardType:     "CAR",
			expectedError: "",
			expectNil:     false,
		},
		{
			name: "missing_media_url",
			changeDoc: bson.M{
				"_id":       testID,
				"ListingId": "123",
				"Media": []bson.M{
					{
						"key": "test",
					},
				},
				"ts": primitive.NewDateTimeFromTime(time.Now()),
			},
			boardType:     "CAR",
			expectedError: "",
			expectNil:     false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Insert test data if needed
			if tc.boardType != "" {
				mergedColl := gomongo.Coll("rni", BoardMergedTable[tc.boardType])
				if mergedColl == nil {
					t.Fatalf("Failed to get merged collection for board: %s", tc.boardType)
				}
				// Clean up existing data
				_, err := mergedColl.DeleteMany(context.Background(), bson.M{"_id": testID})
				if err != nil {
					t.Fatalf("Failed to cleanup existing data: %v", err)
				}
			}

			result, err := analyzer.Analyze(tc.changeDoc, tc.boardType, "ca6")
			if tc.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedError)
			} else {
				assert.NoError(t, err)
			}
			if tc.expectNil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
			}
		})
	}
}

func TestMediaDiffAnalyzer_InvalidBoard(t *testing.T) {
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	testCases := []struct {
		name          string
		board         string
		expectedError bool
		errorContains string
	}{
		{
			name:          "empty_board",
			board:         "",
			expectedError: true,
			errorContains: "invalid board",
		},
		{
			name:          "invalid_board",
			board:         "INVALID",
			expectedError: true,
			errorContains: "invalid board",
		},
		{
			name:          "lowercase_board",
			board:         "car",
			expectedError: true,
			errorContains: "invalid board",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			analyzer := NewMediaDiffAnalyzer()
			newProp := bson.M{
				"_id":       "test123",
				"ListingId": "test123",
				"Media":     []bson.M{},
				"ts":        primitive.NewDateTimeFromTime(time.Now()),
			}

			result, err := analyzer.Analyze(newProp, tc.board, "ca6")

			if tc.expectedError {
				assert.Error(t, err)
				if tc.errorContains != "" {
					assert.Contains(t, err.Error(), tc.errorContains)
				}
				assert.NotNil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

func TestMediaDiffAnalyzer_InvalidOperationType(t *testing.T) {
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	testCases := []struct {
		name          string
		newProp       bson.M
		expectedError bool
		errorContains string
	}{
		{
			name: "empty_operation_type",
			newProp: bson.M{
				"_id": "test123",
				// Missing ListingId
			},
			expectedError: true,
			errorContains: "invalid listingId or _id field format in newProp",
		},
		{
			name: "invalid_operation_type",
			newProp: bson.M{
				"_id": "test123",
				// Missing ListingId
			},
			expectedError: true,
			errorContains: "invalid listingId or _id field format in newProp",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			analyzer := NewMediaDiffAnalyzer()

			result, err := analyzer.Analyze(tc.newProp, "CAR", "ca6")

			if tc.expectedError {
				assert.Error(t, err)
				if tc.errorContains != "" {
					assert.Contains(t, err.Error(), tc.errorContains)
				}
				assert.Empty(t, result.DownloadTasks)
				assert.Empty(t, result.DeleteTasks)
				assert.Empty(t, result.PhoLH)
				assert.Empty(t, result.DocLH)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

func TestPrepareMediaTasks_Comprehensive(t *testing.T) {
	analyzer := NewMediaDiffAnalyzer()

	t.Run("valid_document_hash_format", func(t *testing.T) {
		t.Skip("Skipping due to external dependency on GetFullFilePathForProp")
	})

	t.Run("invalid_document_hash_format_single_part", func(t *testing.T) {
		info := &MediaTaskInfo{
			Sid:          "test123",
			NewMedia:     []bson.M{},
			OldPhoLHList: []int32{},
			OldDocLHList: []string{"invalidhash"}, // No extension
			Timestamp:    time.Now(),
			Board:        "CAR",
		}

		_, _, err := analyzer.prepareMediaTasks(info)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid document hash format")
	})

	t.Run("invalid_document_hash_format_non_numeric", func(t *testing.T) {
		info := &MediaTaskInfo{
			Sid:          "test123",
			NewMedia:     []bson.M{},
			OldPhoLHList: []int32{},
			OldDocLHList: []string{"notanumber.pdf"}, // Non-numeric hash
			Timestamp:    time.Now(),
			Board:        "CAR",
		}

		_, _, err := analyzer.prepareMediaTasks(info)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to convert hash to int")
	})

	t.Run("media_with_zero_hash", func(t *testing.T) {
		info := &MediaTaskInfo{
			Sid: "test123",
			NewMedia: []bson.M{
				{
					"MediaKey":      "", // Empty key will generate hash 0
					"MediaURL":      "http://example.com/test.jpg",
					"MediaCategory": "Photo",
				},
			},
			OldPhoLHList: []int32{},
			OldDocLHList: []string{},
			Timestamp:    time.Now(),
			Board:        "CAR",
		}

		downloadTasks, deleteTasks, err := analyzer.prepareMediaTasks(info)
		assert.NoError(t, err)
		// Media with hash 0 should be skipped
		assert.Equal(t, 0, len(downloadTasks))
		assert.Equal(t, 0, len(deleteTasks))
	})

	t.Run("photo_and_document_processing", func(t *testing.T) {
		info := &MediaTaskInfo{
			Sid: "test123",
			NewMedia: []bson.M{
				{
					"MediaKey":      "photo1",
					"MediaURL":      "http://example.com/photo1.jpg",
					"MediaCategory": "Photo",
				},
				{
					"MediaKey":      "doc1",
					"MediaURL":      "http://example.com/doc1.pdf",
					"MediaCategory": "Document",
				},
			},
			OldPhoLHList: []int32{},
			OldDocLHList: []string{},
			Timestamp:    time.Now(),
			Board:        "CAR",
		}

		downloadTasks, deleteTasks, err := analyzer.prepareMediaTasks(info)
		assert.NoError(t, err)
		assert.Equal(t, 2, len(downloadTasks)) // Both should be downloaded
		assert.Equal(t, 0, len(deleteTasks))
	})

	t.Run("treb_board_media_category", func(t *testing.T) {
		info := &MediaTaskInfo{
			Sid: "test123",
			NewMedia: []bson.M{
				{
					"key": "photo1",
					"url": "http://example.com/photo1.jpg",
					"cat": "Photo", // TREB uses "cat" instead of "MediaCategory"
				},
			},
			OldPhoLHList: []int32{},
			OldDocLHList: []string{},
			Timestamp:    time.Now(),
			Board:        "TRB",
		}

		downloadTasks, deleteTasks, err := analyzer.prepareMediaTasks(info)
		assert.NoError(t, err)
		assert.Equal(t, 1, len(downloadTasks))
		assert.Equal(t, 0, len(deleteTasks))
	})
}

func TestSelectBestPhoto(t *testing.T) {
	sizePriority := []string{"Largest", "Large", "Medium", "Small", "Thumbnail"}

	t.Run("empty_photos", func(t *testing.T) {
		result := selectBestPhoto([]bson.M{}, sizePriority)
		assert.Nil(t, result)
	})

	t.Run("single_photo_no_size_desc", func(t *testing.T) {
		photos := []bson.M{
			{"id": 1, "url": "http://example.com/1.jpg"},
		}
		result := selectBestPhoto(photos, sizePriority)
		assert.NotNil(t, result)
		assert.Equal(t, 1, (*result)["id"])
	})

	t.Run("single_photo_with_size_desc", func(t *testing.T) {
		photos := []bson.M{
			{"id": 1, "url": "http://example.com/1.jpg", "sizeDesc": "Large"},
		}
		result := selectBestPhoto(photos, sizePriority)
		assert.NotNil(t, result)
		assert.Equal(t, 1, (*result)["id"])
	})

	t.Run("multiple_photos_select_largest", func(t *testing.T) {
		photos := []bson.M{
			{"id": 1, "url": "http://example.com/1.jpg", "sizeDesc": "Small"},
			{"id": 2, "url": "http://example.com/2.jpg", "sizeDesc": "Largest"},
			{"id": 3, "url": "http://example.com/3.jpg", "sizeDesc": "Medium"},
		}
		result := selectBestPhoto(photos, sizePriority)
		assert.NotNil(t, result)
		assert.Equal(t, 2, (*result)["id"]) // Should select "Largest"
	})

	t.Run("multiple_photos_select_large_when_largest_not_available", func(t *testing.T) {
		photos := []bson.M{
			{"id": 1, "url": "http://example.com/1.jpg", "sizeDesc": "Small"},
			{"id": 2, "url": "http://example.com/2.jpg", "sizeDesc": "Large"},
			{"id": 3, "url": "http://example.com/3.jpg", "sizeDesc": "Medium"},
		}
		result := selectBestPhoto(photos, sizePriority)
		assert.NotNil(t, result)
		assert.Equal(t, 2, (*result)["id"]) // Should select "Large"
	})

	t.Run("no_matching_size_return_first", func(t *testing.T) {
		photos := []bson.M{
			{"id": 1, "url": "http://example.com/1.jpg", "sizeDesc": "Unknown"},
			{"id": 2, "url": "http://example.com/2.jpg", "sizeDesc": "Custom"},
		}
		result := selectBestPhoto(photos, sizePriority)
		assert.NotNil(t, result)
		assert.Equal(t, 1, (*result)["id"]) // Should return first photo
	})

	t.Run("empty_size_desc_return_first", func(t *testing.T) {
		photos := []bson.M{
			{"id": 1, "url": "http://example.com/1.jpg", "sizeDesc": ""},
			{"id": 2, "url": "http://example.com/2.jpg", "sizeDesc": "Large"},
		}
		result := selectBestPhoto(photos, sizePriority)
		assert.NotNil(t, result)
		assert.Equal(t, 1, (*result)["id"]) // Should return first photo due to empty sizeDesc
	})

	t.Run("invalid_size_desc_type", func(t *testing.T) {
		photos := []bson.M{
			{"id": 1, "url": "http://example.com/1.jpg", "sizeDesc": 123}, // Invalid type
			{"id": 2, "url": "http://example.com/2.jpg", "sizeDesc": "Large"},
		}
		result := selectBestPhoto(photos, sizePriority)
		assert.NotNil(t, result)
		assert.Equal(t, 1, (*result)["id"]) // Should return first photo due to invalid sizeDesc type
	})
}

func TestGetNewMediaInfo_Comprehensive(t *testing.T) {
	analyzer := NewMediaDiffAnalyzer()

	t.Run("valid_regular_board", func(t *testing.T) {
		newProp := bson.M{
			"ListingId": "test123",
			"_id":       "test123",
			"Media": []bson.M{
				{
					"MediaKey":      "key1",
					"MediaURL":      "http://example.com/1.jpg",
					"MediaCategory": "Photo",
					"MimeType":      "image/jpeg",
				},
			},
			"ts": primitive.NewDateTimeFromTime(time.Now()),
		}

		info, err := analyzer.getNewMediaInfo(newProp, "CAR")
		assert.NoError(t, err)
		assert.NotNil(t, info)
		assert.Equal(t, "test123", info.Sid)
		assert.Equal(t, "test123", info.ID)
		assert.Equal(t, 1, len(info.NewMediaList))
	})

	t.Run("valid_treb_board", func(t *testing.T) {
		newProp := bson.M{
			"ListingKey": "test123",
			"_id":        "test123",
			"media": []bson.M{
				{
					"id":     1, // Required for TREB board
					"key":    "key1",
					"url":    "http://example.com/1.jpg",
					"cat":    "Photo",
					"tp":     "image/jpeg",
					"status": "Active", // Required for TREB board
				},
			},
			"ts": primitive.NewDateTimeFromTime(time.Now()),
		}

		info, err := analyzer.getNewMediaInfo(newProp, "TRB")
		assert.NoError(t, err)
		assert.NotNil(t, info)
		assert.Equal(t, "test123", info.Sid)
		assert.Equal(t, "test123", info.ID)
		assert.Equal(t, 1, len(info.NewMediaList))
	})

	t.Run("missing_listing_id", func(t *testing.T) {
		newProp := bson.M{
			"_id": "test123",
			// Missing ListingId
			"Media": []bson.M{},
			"ts":    primitive.NewDateTimeFromTime(time.Now()),
		}

		_, err := analyzer.getNewMediaInfo(newProp, "CAR")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid listingId or _id field format")
	})

	t.Run("missing_id", func(t *testing.T) {
		newProp := bson.M{
			"ListingId": "test123",
			// Missing _id
			"Media": []bson.M{},
			"ts":    primitive.NewDateTimeFromTime(time.Now()),
		}

		_, err := analyzer.getNewMediaInfo(newProp, "CAR")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid listingId or _id field format")
	})

	t.Run("invalid_media_type", func(t *testing.T) {
		newProp := bson.M{
			"ListingId": "test123",
			"_id":       "test123",
			"Media":     "invalid", // Should be array
			"ts":        primitive.NewDateTimeFromTime(time.Now()),
		}

		_, err := analyzer.getNewMediaInfo(newProp, "CAR")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid media field format")
	})

	t.Run("invalid_ts_type", func(t *testing.T) {
		newProp := bson.M{
			"ListingId": "test123",
			"_id":       "test123",
			"Media":     []bson.M{},
			"ts":        "invalid", // Should be DateTime
		}

		_, err := analyzer.getNewMediaInfo(newProp, "CAR")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no valid timestamp found in record for board CAR")
	})

	t.Run("missing_ts_field", func(t *testing.T) {
		newProp := bson.M{
			"ListingId": "test123",
			"_id":       "test123",
			"Media":     []bson.M{},
			// Missing ts field
		}

		_, err := analyzer.getNewMediaInfo(newProp, "CAR")
		// Should error because ts field is required
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no valid timestamp found in record for board CAR")
	})

}

func TestExtractMediaInfo_TrebCategoryFallback(t *testing.T) {
	tests := []struct {
		name             string
		media            bson.M
		expectedCategory string
		expectedIsPhoto  bool
	}{
		{
			name: "TREB media with cat field",
			media: bson.M{
				"key": "key1",
				"url": "http://example.com/1.jpg",
				"tp":  "image/jpeg",
				"cat": "Photo",
			},
			expectedCategory: "Photo",
			expectedIsPhoto:  true,
		},
		{
			name: "TREB media without cat field - image/jpeg",
			media: bson.M{
				"key": "key1",
				"url": "http://example.com/1.jpg",
				"tp":  "image/jpeg",
			},
			expectedCategory: "Photo",
			expectedIsPhoto:  true,
		},
		{
			name: "TREB media without cat field - image/png",
			media: bson.M{
				"key": "key1",
				"url": "http://example.com/1.png",
				"tp":  "image/png",
			},
			expectedCategory: "Photo",
			expectedIsPhoto:  true,
		},
		{
			name: "TREB media without cat field - image/gif",
			media: bson.M{
				"key": "key1",
				"url": "http://example.com/1.gif",
				"tp":  "image/gif",
			},
			expectedCategory: "Photo",
			expectedIsPhoto:  true,
		},
		{
			name: "TREB media without cat field - jpeg only",
			media: bson.M{
				"key": "key1",
				"url": "http://example.com/1.jpg",
				"tp":  "jpeg",
			},
			expectedCategory: "Photo",
			expectedIsPhoto:  true,
		},
		{
			name: "TREB media without cat field - immge/jpeg (typo)",
			media: bson.M{
				"key": "key1",
				"url": "http://example.com/1.jpg",
				"tp":  "immge/jpeg",
			},
			expectedCategory: "Photo",
			expectedIsPhoto:  true,
		},
		{
			name: "TREB media without cat field - JPEG uppercase",
			media: bson.M{
				"key": "key1",
				"url": "http://example.com/1.jpg",
				"tp":  "image/JPEG",
			},
			expectedCategory: "Photo",
			expectedIsPhoto:  true,
		},
		{
			name: "TREB media without cat field - application/pdf",
			media: bson.M{
				"key": "key1",
				"url": "http://example.com/1.pdf",
				"tp":  "application/pdf",
			},
			expectedCategory: "other",
			expectedIsPhoto:  false,
		},
		{
			name: "TREB media without cat field - text/plain",
			media: bson.M{
				"key": "key1",
				"url": "http://example.com/1.txt",
				"tp":  "text/plain",
			},
			expectedCategory: "other",
			expectedIsPhoto:  false,
		},
		{
			name: "TREB media without cat field - video/mp4",
			media: bson.M{
				"key": "key1",
				"url": "http://example.com/1.mp4",
				"tp":  "video/mp4",
			},
			expectedCategory: "other",
			expectedIsPhoto:  false,
		},
		{
			name: "TREB media without cat and tp fields",
			media: bson.M{
				"key": "key1",
				"url": "http://example.com/1.jpg",
			},
			expectedCategory: "other",
			expectedIsPhoto:  false,
		},
		{
			name: "TREB media with empty cat field - image/jpeg",
			media: bson.M{
				"key": "key1",
				"url": "http://example.com/1.jpg",
				"tp":  "image/jpeg",
				"cat": "",
			},
			expectedCategory: "Photo",
			expectedIsPhoto:  true,
		},
		{
			name: "TREB media with empty cat field - application/pdf",
			media: bson.M{
				"key": "key1",
				"url": "http://example.com/1.pdf",
				"tp":  "application/pdf",
				"cat": "",
			},
			expectedCategory: "other",
			expectedIsPhoto:  false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			analyzer := NewMediaDiffAnalyzer()
			info, err := analyzer.extractMediaInfo(tc.media, true) // isTreb = true

			assert.NoError(t, err)
			assert.NotNil(t, info)
			assert.Equal(t, tc.expectedCategory, info.Category)
			assert.Equal(t, tc.expectedIsPhoto, info.IsPhoto)
		})
	}
}

func TestEnsureCatFieldForTRB(t *testing.T) {
	tests := []struct {
		name        string
		media       bson.M
		expectedCat string
	}{
		{
			name: "Media with existing cat field",
			media: bson.M{
				"key": "key1",
				"tp":  "image/jpeg",
				"cat": "Photo",
			},
			expectedCat: "Photo",
		},
		{
			name: "Media without cat field - image/jpeg",
			media: bson.M{
				"key": "key1",
				"tp":  "image/jpeg",
			},
			expectedCat: "Photo",
		},
		{
			name: "Media without cat field - image/png",
			media: bson.M{
				"key": "key1",
				"tp":  "image/png",
			},
			expectedCat: "Photo",
		},
		{
			name: "Media without cat field - contains jpeg",
			media: bson.M{
				"key": "key1",
				"tp":  "jpeg",
			},
			expectedCat: "Photo",
		},
		{
			name: "Media without cat field - application/pdf",
			media: bson.M{
				"key": "key1",
				"tp":  "application/pdf",
			},
			expectedCat: "",
		},
		{
			name: "Media with empty cat field - image/jpeg",
			media: bson.M{
				"key": "key1",
				"tp":  "image/jpeg",
				"cat": "",
			},
			expectedCat: "Photo",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			analyzer := NewMediaDiffAnalyzer()
			analyzer.ensureCatFieldForTRB(tc.media)

			if tc.expectedCat == "" {
				// Should not have cat field or should be empty
				cat, exists := tc.media["cat"]
				if exists {
					assert.Equal(t, "", cat)
				}
			} else {
				// Should have the expected cat value
				cat, exists := tc.media["cat"]
				assert.True(t, exists)
				assert.Equal(t, tc.expectedCat, cat)
			}
		})
	}
}

func TestExtractMediaInfo_RegularBoardCategory(t *testing.T) {
	tests := []struct {
		name             string
		media            bson.M
		expectedCategory string
		expectedIsPhoto  bool
	}{
		{
			name: "Regular board media with MediaCategory",
			media: bson.M{
				"MediaKey":      "key1",
				"MediaURL":      "http://example.com/1.jpg",
				"MimeType":      "image/jpeg",
				"MediaCategory": "Photo",
			},
			expectedCategory: "Photo",
			expectedIsPhoto:  true,
		},
		{
			name: "Regular board media without MediaCategory",
			media: bson.M{
				"MediaKey": "key1",
				"MediaURL": "http://example.com/1.jpg",
				"MimeType": "image/jpeg",
			},
			expectedCategory: "",
			expectedIsPhoto:  false,
		},
		{
			name: "Regular board media with Document category",
			media: bson.M{
				"MediaKey":      "key1",
				"MediaURL":      "http://example.com/1.pdf",
				"MimeType":      "application/pdf",
				"MediaCategory": "Document",
			},
			expectedCategory: "Document",
			expectedIsPhoto:  false,
		},
		{
			name: "Regular board media with empty MediaCategory",
			media: bson.M{
				"MediaKey":      "key1",
				"MediaURL":      "http://example.com/1.jpg",
				"MimeType":      "image/jpeg",
				"MediaCategory": "",
			},
			expectedCategory: "",
			expectedIsPhoto:  false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			analyzer := NewMediaDiffAnalyzer()
			info, err := analyzer.extractMediaInfo(tc.media, false) // isTreb = false

			assert.NoError(t, err)
			assert.NotNil(t, info)
			assert.Equal(t, tc.expectedCategory, info.Category)
			assert.Equal(t, tc.expectedIsPhoto, info.IsPhoto)
		})
	}
}

func TestGenerateHashListWithMediaTypes(t *testing.T) {
	analyzer := NewMediaDiffAnalyzer()

	tests := []struct {
		name    string
		medias  []bson.M
		wantPho []int32
		wantDoc []string
	}{
		{
			name: "Audio file with tp field",
			medias: []bson.M{
				{
					"key": "trreb/w9355038/audio",
					"tp":  "audio/mpeg",
					"url": "https://example.com/audio.mp3",
				},
			},
			wantPho: nil,
			wantDoc: []string{fmt.Sprintf("%d.mp3", levelStore.MurmurToInt32("trreb/w9355038/audio"))},
		},
		{
			name: "PDF with MimeType field",
			medias: []bson.M{
				{
					"MediaKey": "doc1",
					"MimeType": "application/pdf",
					"MediaURL": "https://example.com/doc.pdf",
				},
			},
			wantPho: nil,
			wantDoc: []string{fmt.Sprintf("%d.pdf", levelStore.MurmurToInt32("doc1"))},
		},
		{
			name: "JPEG with tp field",
			medias: []bson.M{
				{
					"key": "photo1",
					"tp":  "image/jpeg",
					"cat": "Photo",
					"url": "https://example.com/photo.jpg",
				},
			},
			wantPho: []int32{levelStore.MurmurToInt32("photo1")},
			wantDoc: nil,
		},
		{
			name: "Mixed media types with different fields",
			medias: []bson.M{
				{
					"key": "audio1",
					"tp":  "audio/mpeg",
					"url": "https://example.com/audio.mp3",
				},
				{
					"MediaKey": "doc1",
					"MimeType": "application/pdf",
					"MediaURL": "https://example.com/doc.pdf",
				},
				{
					"key": "photo1",
					"tp":  "image/jpeg",
					"cat": "Photo",
					"url": "https://example.com/photo.jpg",
				},
			},
			wantPho: []int32{levelStore.MurmurToInt32("photo1")},
			wantDoc: []string{
				fmt.Sprintf("%d.mp3", levelStore.MurmurToInt32("audio1")),
				fmt.Sprintf("%d.pdf", levelStore.MurmurToInt32("doc1")),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			phoLH, docLH := analyzer.generateHashList(tt.medias)
			assert.Equal(t, tt.wantPho, phoLH)
			assert.Equal(t, tt.wantDoc, docLH)
		})
	}
}

func TestGetFileExtensionFromMimeType(t *testing.T) {
	tests := []struct {
		mimeType string
		expected string
	}{
		// Document types
		{"application/pdf", ".pdf"},
		{"application/msword", ".doc"},
		{"application/vnd.openxmlformats-officedocument.wordprocessingml.document", ".docx"},
		{"application/vnd.ms-excel", ".xls"},
		{"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", ".xlsx"},
		{"application/rtf", ".rtf"},
		{"text/rtf", ".rtf"},
		{"application/vnd.oasis.opendocument.text", ".odt"},
		{"application/x-tika-msoffice", ".doc"},
		{"application/x-tika-ooxml", ".docx"},

		// Text types
		{"text/plain", ".txt"},
		{"text/html", ".html"},
		{"text/xml", ".xml"},

		// Audio types
		{"audio/mpeg", ".mp3"},

		// Video types
		{"video/mp4", ".mp4"},
		{"video/quicktime", ".mov"},

		// Archive types
		{"application/zip", ".zip"},

		// Binary/Other types
		{"application/octet-stream", ".bin"},
		{"application/x-dosexec", ".exe"},
		{"application/x-bplist", ".plist"},
		{"multipart/appledouble", ".appledouble"},
		{"url", ".url"},

		// Image types
		{"image/jpeg", ".jpg"},
		{"immge/jpeg", ".jpg"}, // Handle typo
		{"jpeg", ".jpg"},
		{"image/png", ".png"},
		{"image/gif", ".gif"},
		{"image/webp", ".webp"},
		{"image/bmp", ".bmp"},
		{"image/tiff", ".tiff"},
		{"image/svg+xml", ".svg"},
		{"image/heic", ".heic"},
		{"image/vnd.microsoft.icon", ".ico"},

		// Unknown types
		{"unknown/type", ".jpg"},
		{"", ".jpg"},
	}

	for _, tt := range tests {
		t.Run(tt.mimeType, func(t *testing.T) {
			result := getFileExtensionFromMimeType(tt.mimeType)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestGetNewMediaInfo_PhoPLogic tests the phoP field logic in getNewMediaInfo
func TestGetNewMediaInfo_PhoPLogic(t *testing.T) {
	analyzer := NewMediaDiffAnalyzer()

	testTime1 := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
	testTime2 := time.Date(2023, 2, 1, 12, 0, 0, 0, time.UTC)
	testTime3 := time.Date(2023, 3, 1, 12, 0, 0, 0, time.UTC)
	testTime4 := time.Date(2023, 4, 1, 12, 0, 0, 0, time.UTC)

	tests := []struct {
		name           string
		newProp        bson.M
		board          string
		expectPhoPSkip bool
		expectedError  bool
	}{
		{
			name: "TRB board - no phoP field, should generate with priority timestamps",
			newProp: bson.M{
				"ListingKey":             "test123",
				"_id":                    "test123",
				"ListingContractDate":    testTime1,
				"ModificationTimestamp":  testTime2,
				"OriginalEntryTimestamp": testTime3,
				"ts":                     testTime4,
				"media": []bson.M{
					{
						"id":     1,
						"key":    "key1",
						"url":    "http://example.com/1.jpg",
						"cat":    "Photo",
						"tp":     "image/jpeg",
						"status": "Active",
					},
				},
			},
			board:          "TRB",
			expectPhoPSkip: false,
			expectedError:  false,
		},
		{
			name: "CAR board - no phoP field, should generate with priority timestamps",
			newProp: bson.M{
				"ListingId":           "test456",
				"_id":                 "test456",
				"ListingContractDate": testTime1,
				"ts":                  testTime4,
				"Media": []bson.M{
					{
						"MediaKey":      "key1",
						"MediaURL":      "http://example.com/1.jpg",
						"MediaCategory": "Photo",
						"MimeType":      "image/jpeg",
					},
				},
			},
			board:          "CAR",
			expectPhoPSkip: false,
			expectedError:  false,
		},
		{
			name: "TRB board - existing phoP field, should skip generation",
			newProp: bson.M{
				"ListingKey": "test789",
				"_id":        "test789",
				"phoP":       "/1234/abc12",
				"ts":         testTime4,
				"media": []bson.M{
					{
						"id":     1,
						"key":    "key1",
						"url":    "http://example.com/1.jpg",
						"cat":    "Photo",
						"tp":     "image/jpeg",
						"status": "Active",
					},
				},
			},
			board:          "TRB",
			expectPhoPSkip: true,
			expectedError:  false,
		},
		{
			name: "CAR board - empty phoP field, should generate",
			newProp: bson.M{
				"ListingId":           "test101112",
				"_id":                 "test101112",
				"phoP":                "",
				"ListingContractDate": testTime1,
				"ts":                  testTime4,
				"Media": []bson.M{
					{
						"MediaKey":      "key1",
						"MediaURL":      "http://example.com/1.jpg",
						"MediaCategory": "Photo",
						"MimeType":      "image/jpeg",
					},
				},
			},
			board:          "CAR",
			expectPhoPSkip: false,
			expectedError:  false,
		},
		{
			name: "TRB board - nil phoP field, should generate",
			newProp: bson.M{
				"ListingKey":             "test131415",
				"_id":                    "test131415",
				"phoP":                   nil,
				"ListingContractDate":    testTime1,
				"ModificationTimestamp":  testTime2,
				"OriginalEntryTimestamp": testTime3,
				"ts":                     testTime4,
				"media": []bson.M{
					{
						"id":     1,
						"key":    "key1",
						"url":    "http://example.com/1.jpg",
						"cat":    "Photo",
						"tp":     "image/jpeg",
						"status": "Active",
					},
				},
			},
			board:          "TRB",
			expectPhoPSkip: false,
			expectedError:  false,
		},
		{
			name: "TRB board - no valid timestamp fields, should error",
			newProp: bson.M{
				"ListingKey": "test161718",
				"_id":        "test161718",
				"media": []bson.M{
					{
						"id":     1,
						"key":    "key1",
						"url":    "http://example.com/1.jpg",
						"cat":    "Photo",
						"tp":     "image/jpeg",
						"status": "Active",
					},
				},
			},
			board:         "TRB",
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			info, err := analyzer.getNewMediaInfo(tt.newProp, tt.board)

			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, info)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, info)

				// 验证基本字段
				if tt.board == "TRB" {
					assert.Equal(t, tt.newProp["ListingKey"], info.Sid)
				} else {
					assert.Equal(t, tt.newProp["ListingId"], info.Sid)
				}
				assert.Equal(t, tt.newProp["_id"], info.ID)

				// 验证媒体列表
				assert.NotEmpty(t, info.NewMediaList)

				// 验证时间戳字段（用于兼容性）
				assert.NotZero(t, info.Timestamp)
			}
		})
	}
}
